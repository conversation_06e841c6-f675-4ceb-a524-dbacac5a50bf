<template>
	<view>
		<!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack>
			<block slot="backText">返回</block>
			<block slot="content">品牌宣传</block>
			<block slot="right">
				<a @click="screen">
					筛选
				</a>
			</block>
		</cu-custom>

		<!-- 筛选弹窗 -->
		<u-popup :show="showPopup" mode="right" :closeable="true" @close="closePopup">
			<view style='padding: 5%;'>
				<u-form ref="uForm" style="overflow: scroll;height: 50vh;">
					<u-form-item label="文件名称">
						<u-input v-model="queryParam.name" />
					</u-form-item>
					<!-- <u-form-item label="状态">
						<uni-data-select style='width: 100%;' v-model="queryParam.type" :localdata="[
							{
								text: '全部',
								value: ''
							},
							{
								text: '图片',
								value: '1'
							},
							{
								text: '视频',
								value: '2'
							},
							{
								text: 'PDF',
								value: '3'
							}
						]" :clear="false">
						</uni-data-select>
					</u-form-item> -->
				</u-form>
			</view>
			<u-button type="primary" @click="loadList">搜索</u-button>
		</u-popup>

		<!-- 文件预览弹窗 -->
		<u-popup :show="showPreviewPopup" mode="center" :closeable="true" @close="closePreviewPopup"
			:customStyle="{ width: '95%', height: '90%', borderRadius: '10px' }">
			<view style="height: 100%; display: flex; flex-direction: column;">
				<!-- 弹窗标题 -->
				<view
					style="padding: 15px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
					<text style="font-size: 16px; font-weight: bold;">文件预览</text>
				</view>

				<!-- 预览容器 -->
				<view style="flex: 1; padding: 10px; position: relative;">
					<!-- :src="videoUrl" -->
					<!-- 视频预览 -->
					<video v-if="currentPreviewItem && currentPreviewItem.type === '2' && videoUrl"
						style="width: 100%; height: 100%; border-radius: 5px;" :src="videoUrl" controls
						@loadstart="onVideoLoadStart" @loadeddata="onVideoLoaded" @error="onVideoError">
					</video>

					<!-- 其他文件类型预览 -->
					<template v-else>
						<!-- #ifdef H5 -->
						<iframe v-if="previewUrl" :src="previewUrl"
							style="width: 100%; height: 100%; border: none; border-radius: 5px; background: #f5f5f5;"
							frameborder="0" @load="onIframeLoad" @error="onIframeError">
						</iframe>
						<!-- #endif -->

						<!-- #ifdef MP || APP-PLUS -->
						<web-view v-if="previewUrl" :src="previewUrl" style="width: 100%; height: 100%;"
							@load="onWebViewLoad" @error="onWebViewError">
						</web-view>
						<!-- #endif -->
					</template>

					<!-- 加载提示 -->
					<!-- <view v-if="isLoading || (!previewUrl && !videoUrl)"
						style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; justify-content: center; align-items: center; background: rgba(255,255,255,0.9);">
						<view style="text-align: center;">
							<u-loading-icon mode="spinner" size="30" color="#409eff"></u-loading-icon>
							<text style="margin-top: 10px; color: #666; font-size: 14px;">正在加载预览...</text>
						</view>
					</view> -->

					<!-- 错误提示 -->
					<view v-if="previewError"
						style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; justify-content: center; align-items: center; background: #f5f5f5;">
						<view style="text-align: center; padding: 20px;">
							<u-icon name="error-circle" size="50" color="#f56c6c"></u-icon>
							<text style="display: block; margin-top: 15px; color: #666; font-size: 14px;">预览加载失败</text>
							<text style="display: block; margin-top: 5px; color: #999; font-size: 12px;">{{ previewError
							}}</text>
							<u-button type="primary" size="mini" style="margin-top: 15px;" @click="retryPreview">
								重新加载
							</u-button>
						</view>
					</view>
				</view>
			</view>
		</u-popup>
		<!--滚动加载列表-->
		<mescroll-body ref="mescrollRef" bottom="88" @init="mescrollInit" :up="upOption" :down="downOption"
			@down="downCallback" @up="upCallback">
			<view class="cu-list menu">
				<view class="cu-item" v-for="(item, index) in list" :key="index" @click="detail(item)">
					<view
						style="text-align: left;background-color: #fff;padding:3%;border-radius: 10px;width: 95%;margin: 0.3rem auto; margin-bottom: 3%;box-shadow: 2px 2px 10px #666666;">
						<view class="u-hairline--bottom" style="margin-bottom:0.3rem;">
							<u-row>
								<u-col span="12">
									<span style="font-size:18px;font-weight: 700;color: #000;">
										{{ item.name }}
									</span>
								</u-col>

							</u-row>
							<u-row>
								<u-col span="4">
									<span style="font-size:14px;font-weight: 700;color: #000;">
										类型
									</span>
								</u-col>
								<u-col span="8">
									<span style="font-size:14px;color: #666;">
										{{ item.type === '1' ? '图片' : item.type === '2' ? '视频' : item.type === '3' ?
											'PDF' : '' }}
									</span>
								</u-col>
							</u-row>
							<u-row>
								<u-col span="4">
									<span style="font-size:14px;font-weight: 700;color: #000;">
										创建人
									</span>
								</u-col>
								<u-col span="8">
									<span style="font-size:14px;color: #666;">
										{{ item.creator }}-{{ item.realname }}
									</span>
								</u-col>
							</u-row>
							<u-row>
								<u-col span="4">
									<span style="font-size:14px;font-weight: 700;color: #000;">
										创建时间
									</span>
								</u-col>
								<u-col span="8">
									<span style="font-size:14px;color: #666;">
										{{ item.create_time }}
									</span>
								</u-col>
							</u-row>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
import Mixin from "@/common/mixin/Mixin.js";
import { encryptByBase64 } from "@/common/util/util.js";
import configService from '@/common/service/config.service.js';
export default {
	name: 'userInfo',
	mixins: [MescrollMixin, Mixin],
	components: {
	},
	data() {
		return {
			CustomBar: this.CustomBar,
			NavBarColor: this.NavBarColor,
			url: "/online/cgreport/api/getData/1946010575932424193",
			// 预览弹窗相关数据
			showPreviewPopup: false,
			previewUrl: '',
			videoUrl: '',
			currentPreviewItem: null,
			isLoading: false,
			previewError: '',
			// 观看记录相关数据
			watchRecordId: null,        // 当前观看记录ID
			watchTimer: null,           // 定时器
			watchStartTime: null,       // 开始观看时间
			watchDuration: 0            // 累计观看时长（秒）
		};
	},
	created() {
	},

	/**
	 * 页面销毁时清理定时器
	 */
	beforeDestroy() {
		this.stopWatchTimer();
	},

	/**
	 * 页面隐藏时暂停定时器
	 */
	onHide() {
		this.stopWatchTimer();
	},

	/**
	 * 页面显示时恢复定时器（如果弹窗还在显示）
	 */
	onShow() {
		if (this.showPreviewPopup && this.watchRecordId && !this.watchTimer) {
			this.startWatchTimer();
		}
	},
	methods: {
		getFullImageUrl(url) {
			if (!url) return '';
			if (url.startsWith('http')) {
				return url;
			}
			const domain = configService.staticDomainURL || '';
			// 移除 domain 尾部的 /
			const cleanDomain = domain.endsWith('/') ? domain.slice(0, -1) : domain;
			// 移除 url 头部的 /
			const cleanUrl = url.startsWith('/') ? url.slice(1) : url;

			if (!cleanDomain) return `/${cleanUrl}`;

			return `${cleanDomain}/${cleanUrl}`;
		},
		async detail(item) {

			try {
				// 重置状态
				this.isLoading = true;
				this.previewError = '';
				this.previewUrl = '';
				this.videoUrl = '';

				// 保存当前预览的文件信息
				this.currentPreviewItem = item;

				// 构建文件完整URL
				let res = await this.$http.get('/pm/clockIn/previewPic?objectKey='+item.file  )
				console.log("🚀 ~ detail ~ res:", res)
				// 判断文件类型
				if (item.type === '2') {
					// 视频类型，直接使用原始URL
					this.videoUrl = this.getFullImageUrl(res.data.result);
					console.log("视频URL:", this.videoUrl);
				} else {
					// 其他类型，使用预览服务
					// 对文件URL进行Base64编码后再进行URL编码
					const encodedUrl = encodeURIComponent(encryptByBase64(res));
					// 构建预览服务的完整URL
					this.previewUrl = 'http://fileview.colori.com:8022/onlinePreview?url=' + encodedUrl;
					console.log("预览URL:", this.previewUrl);
				}

				// 显示预览弹窗
				this.showPreviewPopup = true;

				// 创建观看记录
				await this.createWatchRecord(item);

				// 设置超时处理
				setTimeout(() => {
					if (this.isLoading) {
						this.isLoading = false;
					}
				}, 10000); // 10秒超时

			} catch (error) {
				console.error("预览文件出错:", error);
				this.previewError = '预览初始化失败: ' + error.message;
				this.isLoading = false;

				uni.showToast({
					title: '预览失败',
					icon: 'error'
				});
			}
		},

		/**
		 * 关闭文件预览弹窗
		 */
		closePreviewPopup() {
			// 停止观看记录定时器
			this.stopWatchTimer();

			// 重置预览状态
			this.showPreviewPopup = false;
			this.previewUrl = '';
			this.videoUrl = '';
			this.currentPreviewItem = null;
			this.isLoading = false;
			this.previewError = '';

			// 重置观看记录状态
			this.watchRecordId = null;
			this.watchStartTime = null;
			this.watchDuration = 0;
		},

		/**
		 * iframe加载完成
		 */
		onIframeLoad() {
			console.log("iframe加载完成");
			this.isLoading = false;
			this.previewError = '';
		},

		/**
		 * iframe加载错误
		 */
		onIframeError(error) {
			console.error("iframe加载错误:", error);
			this.isLoading = false;
			this.previewError = '预览服务连接失败，请检查网络连接';
		},

		/**
		 * web-view加载完成
		 */
		onWebViewLoad() {
			console.log("web-view加载完成");
			this.isLoading = false;
			this.previewError = '';
		},

		/**
		 * web-view加载错误
		 */
		onWebViewError(error) {
			console.error("web-view加载错误:", error);
			this.isLoading = false;
			this.previewError = '预览服务连接失败，请检查网络连接';
		},

		/**
		 * 视频开始加载
		 */
		onVideoLoadStart() {
			console.log("视频开始加载");
			this.isLoading = true;
			this.previewError = '';
		},

		/**
		 * 视频加载完成
		 */
		onVideoLoaded() {
			console.log("视频加载完成");
			this.isLoading = false;
			this.previewError = '';
		},

		/**
		 * 视频加载错误
		 */
		onVideoError(error) {
			console.error("视频加载错误:", error);
			this.isLoading = false;
			this.previewError = '视频加载失败，请检查网络连接或视频格式';
		},

		/**
		 * 重新加载预览
		 */
		retryPreview() {
			if (this.currentPreviewItem) {
				this.detail(this.currentPreviewItem);
			}
		},

		/**
		 * 创建观看记录
		 * @param {Object} item - 文件项
		 */
		async createWatchRecord(item) {
			try {
				console.log("创建观看记录，文件ID:", item.id);

				// 调用创建观看记录接口
				const response = await this.$http.post('/pm/promotionWatchRecord/add', {
					promotionId: item.id
				});

				if (response.data && response.data.success) {
					// 保存观看记录ID
					this.watchRecordId = response.data.result?.id || response.data.result;
					console.log("观看记录创建成功，ID:", this.watchRecordId);

					// 记录开始观看时间
					this.watchStartTime = Date.now();
					this.watchDuration = 0;

					// 启动定时器，每5秒更新一次观看记录
					this.startWatchTimer();
				} else {
					console.error("创建观看记录失败:", response.data);
				}
			} catch (error) {
				console.error("创建观看记录异常:", error);
			}
		},

		/**
		 * 启动观看记录定时器
		 */
		startWatchTimer() {
			// 清除之前的定时器
			this.stopWatchTimer();

			// 每5秒更新一次观看记录
			this.watchTimer = setInterval(() => {
				this.updateWatchRecord();
			}, 5000);

			console.log("观看记录定时器已启动");
		},

		/**
		 * 停止观看记录定时器
		 */
		stopWatchTimer() {
			if (this.watchTimer) {
				clearInterval(this.watchTimer);
				this.watchTimer = null;
				console.log("观看记录定时器已停止");
			}
		},

		/**
		 * 更新观看记录
		 */
		async updateWatchRecord() {
			if (!this.watchRecordId || !this.watchStartTime || !this.currentPreviewItem) {
				return;
			}

			try {
				// 计算当前观看时长（秒）
				const currentTime = Date.now();
				const totalDuration = Math.floor((currentTime - this.watchStartTime) / 1000);

				console.log("更新观看记录 - 时长:", totalDuration, "秒");

				// 调用更新观看记录接口
				const response = await this.$http.post('/pm/promotionWatchRecord/edit', {
					time: totalDuration,
					promotionId: this.currentPreviewItem.id,
					id: this.watchRecordId
				});

				if (response.data && response.data.success) {
					this.watchDuration = totalDuration;
					console.log("观看记录更新成功，累计时长:", totalDuration, "秒");
				} else {
					console.error("更新观看记录失败:", response.data);
				}
			} catch (error) {
				console.error("更新观看记录异常:", error);
			}
		},
		loadList(flag) {
			let param = this.queryParam
			param.pageNo = this.pageNo,
				param.pageSize = this.pageSize
			console.log("请求参数", param)
			this.$http.get(this._data.url, { params: { ...param, name: `*${param.name ? param.name : ''}*` } }).then(res => {
				this.showPopup = false
				if (res.data.success) {
					console.log("请求返回res.data", res.data)
					let rec = res.data.result.records
					if (flag == 'down') {
						//下拉刷新成功的回调,隐藏下拉刷新的状态
						this.mescroll.endSuccess();
					}
					//添加新数据
					this.list = rec;
					/* if(!rec || rec.length<this.pageSize){
					  console.log("加载完成!")
					} */
				} else {
					console.log("请求返回else", res)
					this.mescroll.endErr();
				}
			}).catch((err) => {
				console.log("请求返回err", err)
				//加载失败, 结束
				this.mescroll.endErr();
			})
		},
	}
}
</script>
